{"eslint.options": {"overrideConfigFile": ".eslintrc.json"}, "eslint.validate": ["javascript", "javascriptreact"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "cSpell.words": ["reduxjs", "sfrefarch", "sigv"], "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}