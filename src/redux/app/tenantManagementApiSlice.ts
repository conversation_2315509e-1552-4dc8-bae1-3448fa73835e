import {ApiSliceIdentifier} from 'Constants/enums';
import {apiSlice} from 'redux/apiSlice';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;
export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    verifyTenantKey: builder.mutation({
      query: KeyDto => ({
        url: '/tenants/verify-key',
        method: 'POST',
        body: KeyDto,
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {useVerifyTenantKeyMutation} = tenantApiSlice;
