import type {BaseQueryFn, FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {ApiSliceIdentifier} from 'Constants/enums';
import {getErrorMessage} from 'Helpers/utils';
import {enqueueSnackbar} from 'notistack';
import {AuthData, setCredentials, unsetCredentials} from './auth/authSlice';
import {getBaseUrl} from './redux.helper';
import type {RootState} from './store';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  {url: string; method?: string; body?: any; apiSliceIdentifier?: ApiSliceIdentifier},
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, {getState}) {
      // const token = (getState() as RootState).auth.accessToken;
      const token =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8g7wOAZrYb7wKzcbr1S3gUY8QPau09Y9HfTpQadZZO8';

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  console.log({result});
  if (
    result.error &&
    result.error.status === RESULT_ERROR_STATUS &&
    typeof result.error.data === 'object' &&
    result.error.data !== null &&
    'message' in result.error.data &&
    (result.error.data as {message?: string}).message !== 'Invalid Credentials'
  ) {
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: '/auth/token-refresh',
        method: 'POST',
        body: {refreshToken: (api.getState() as RootState).auth.refreshToken},
      },
      api,
      extraOptions,
    );
    if (refreshResult.data) {
      // store the new token
      api.dispatch(setCredentials(refreshResult.data as AuthData));
      // retry the initial query
      result = await baseQuery(args, api, extraOptions);
    } else {
      api.dispatch(unsetCredentials());
    }
  } else if (result.error) {
    const errorMessage = getErrorMessage(result.error);
    enqueueSnackbar(`${errorMessage}`, {variant: 'error'});
  }
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
});
