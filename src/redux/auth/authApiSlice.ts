import {apiSlice} from '../apiSlice';
import {User} from './user.model';

export interface ILoginForm {
  username: string;
  password: string;
}

export interface IToken {
  code: string;
}

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation({
      query: (credentials: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    getToken: builder.mutation({
      query: (credentials: IToken) => ({
        url: '/auth/token',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/logout',
        method: 'POST',
        body: {refreshToken},
      }),
    }),
    getUser: builder.query<User, void>({
      query: () => ({url: '/auth/me'}),
    }),
  }),
});

export const {useLoginMutation, useLogoutMutation, useGetTokenMutation, useGetUserQuery} = authApiSlice;
