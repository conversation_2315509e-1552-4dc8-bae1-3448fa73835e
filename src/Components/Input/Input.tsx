import FileCopyIcon from '@mui/icons-material/FileCopy';
import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {
  FormControl,
  FormHelperText,
  IconButton,
  OutlinedInput,
  OutlinedInputProps,
  SxProps,
  Tooltip,
} from '@mui/material';
import InputLabel from 'Components/InputLabel';
import { colors } from 'Providers/theme/colors';
import React, { memo, useCallback, useState } from 'react';

interface AnyErrorObj {
  [key: string]: unknown;
}

export type InputProps = Omit<OutlinedInputProps, 'onChange' | 'onBlur' | 'onFocus'> & {
  id: string;
  label?: string;
  copyEnabled?: boolean;
  errorMessage?: string | AnyErrorObj;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  onChange?: (val: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  helperText?: string;
  sxProps?: SxProps;

  /** ✅ New Props for MUI v7 */
  labelSx?: object;
  inputSx?: React.CSSProperties;
  errorSx?: React.CSSProperties;
};

const getEndAdornment = ({
  copyEnabled,
  value,
  isError,
  endAdornment,
}: {
  copyEnabled: boolean;
  value?: OutlinedInputProps['value'];
  isError: boolean | undefined;
  endAdornment: React.ReactNode;
}) => {
  if (endAdornment && !isError) return endAdornment;
  if (isError) return <ReportProblemOutlinedIcon color="error" />;
  if (copyEnabled)
    return (
      <Tooltip title="Copy to clipboard">
        <IconButton sx={{ cursor: 'pointer' }}>
          <FileCopyIcon />
        </IconButton>
      </Tooltip>
    );
};

const Input: React.FC<InputProps> = ({
  id,
  value,
  label,
  helperText,
  disabled = false,
  endAdornment,
  copyEnabled = false,
  errorMessage,
  onChange,
  labelSx,
  sxProps,
  inputSx = {},
  errorSx = {},
  ...rest
}) => {
  const [focused, setFocused] = useState(false); // To track focus state
  const isError = !!errorMessage;

  const handleChangeEvent = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (onChange) onChange(e?.target?.value);
    },
    [onChange],
  );
  const handleBlur = () => {
    if (!value) {
      setFocused(false);
    }
  };

  return (
    <FormControl sx={{ width: 1 }} data-testid="inputFormControl" error={isError} disabled={disabled}>
      {label && (
        <InputLabel
          htmlFor={id}
          sx={{
            ...labelSx,
            color: colors.black, // default
            '&.Mui-focused': {
              color: colors.black, // keep gray on focus
            },
            '&.MuiFormLabel-root': {
              color: colors.black, // ensure gray in all other states
            },
            fontSize: 17,
          }}
        >
          {label}
        </InputLabel>
      )}
      <OutlinedInput
        disabled={disabled}
        data-testid="input"
        value={value}
        id={id}
        sx={{
          ...sxProps,
        }}
        inputProps={{
          sx: {
            padding: 1,
            ...inputSx, // Merge custom input styles
          },
        }}
        onChange={handleChangeEvent}
        onBlur={handleBlur}
        endAdornment={getEndAdornment({ copyEnabled, value, isError, endAdornment })}
        {...rest}
      />
      {(isError || helperText) && (
        <FormHelperText sx={{ ...errorSx }}>{isError ? <>{errorMessage}</> : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default memo(Input);
