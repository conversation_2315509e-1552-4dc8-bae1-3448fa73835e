export const StepperTabStyles = {
  stepper: {
    backgroundColor: '#fdf3f7', // soft pink background
    borderTopLeftRadius: '0.5rem', // Upper-left corner
    borderTopRightRadius: '0.5rem',
    padding: '0.75rem 1rem',
    border: '0.0625rem solid #e5a8b5', // visible border (as in screenshot)

    '& .MuiStepConnector-root': {
      top: '50%',
      left: 'calc(-50% + 1.25rem)',
      right: 'calc(50% + 1.25rem)',
    },

    '& .MuiStepConnector-line': {
      borderColor: '#e5a8b5', // light pink line
      borderWidth: '0.0625rem',
    },

    '& .MuiStepIcon-root': {
      width: '2rem',
      height: '2rem',
      color: '#ffffff',
      border: '0.0625rem solid #DDDDDD',
      borderRadius: '50%',
      backgroundColor: '#ffffff',

      '& text': {
        fill: '#000000',
      },
      '&.Mui-active': {
        color: '#ffffffff',
        border: '0.0625rem solid #8c1d40',
        backgroundColor: '#fdf3f7',

        '& text': {
          fill: '#8c1d40',
        },
      },
      '&.Mui-completed': {
        color: '#8c1d40',
        border: '0.0625rem solid #8c1d40',
        backgroundColor: '#fdf3f7',
      },
    },

    '& .MuiStepIcon-text': {
      fontSize: '0.875rem',
    },

    '& .MuiStepLabel-label': {
      color: '#6e6e6e',
      fontSize: '0.9375rem',
      fontWeight: '500',

      '&.Mui-active': {
        color: '#8c1d40',
        fontWeight: 'bold',
      },
      '&.Mui-completed': {
        color: '#8c1d40',
      },
    },
  },
};
