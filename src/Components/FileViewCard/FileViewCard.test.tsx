import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import {IFile} from 'types';
import {vi} from 'vitest';
import FileViewCard from './FileViewCard';
import {getFileSize} from './utils';

vi.mock('../../Assets/file.png', () => ({
  default: 'file-icon-mock.png',
}));
// Mock the getFileSize utility
vi.mock('./utils', () => ({
  getFileSize: vi.fn(),
}));

describe('FileViewCard Component', () => {
  const mockFile: IFile = {
    originalName: 'test-document.pdf',
    size: 1024,
  } as IFile;

  beforeEach(() => {
    (getFileSize as unknown as ReturnType<typeof vi.fn>).mockReturnValue('1 KB');
  });

  it('should render file details correctly', () => {
    render(<FileViewCard fileDetail={mockFile} />);

    expect(screen.getByTestId('file-view-card')).toBeInTheDocument();
    expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    expect(screen.getByText('1 KB')).toBeInTheDocument();
    expect(screen.queryByTestId('file-remove-btn')).not.toBeInTheDocument();
  });

  it('should call handleRemoveFile when remove button is clicked', () => {
    const mockRemove = vi.fn();

    render(<FileViewCard fileDetail={mockFile} handleRemoveFile={mockRemove} />);
    const removeButton = screen.getByTestId('file-remove-btn');

    fireEvent.click(removeButton);

    expect(mockRemove).toHaveBeenCalledWith('test-document.pdf');
    expect(mockRemove).toHaveBeenCalledTimes(1);
  });

  it('should display file name when fileDetail is of type File', () => {
    const file = new File(['dummy content'], 'uploaded-file.txt', {type: 'text/plain'});

    render(<FileViewCard fileDetail={file} />);
    expect(screen.getByText('uploaded-file.txt')).toBeInTheDocument();
  });
});
