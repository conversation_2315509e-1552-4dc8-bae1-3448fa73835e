import {colors} from 'Providers/theme/colors';
import React from 'react';
import {Link} from 'react-router-dom';

export interface BreadcrumbItem {
  label: string;
  url?: string;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  separator?: string;
  showHeader?: boolean;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({items = [], separator = '|', showHeader = false}) => {
  const lastLabel = items.length ? items[items.length - 1].label : '';

  return (
    <div style={{display: 'flex', flexDirection: 'column'}}>
      {showHeader && <h2 style={{margin: '0', fontSize: '1.25rem', fontWeight: 'bold'}}>{lastLabel}</h2>}

      <nav style={{display: 'flex', alignItems: 'center', fontSize: '0.625rem', marginBottom: '0.375rem'}}>
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          return (
            <React.Fragment key={index}>
              {!isLast ? (
                <>
                  {item.url ? (
                    <Link
                      to={item.url}
                      style={{
                        textDecoration: 'none',
                        color: colors.dark500,
                        marginRight: '0.5rem',
                        paddingBottom: '0.125rem',
                        borderBottom: `0.0625rem solid ${colors.dark300}`,
                      }}
                    >
                      {item.label}
                    </Link>
                  ) : (
                    <span
                      style={{
                        color: colors.dark500,
                        marginRight: '0.5rem',
                        paddingBottom: '0.125rem',
                        borderBottom: `0.0625rem solid ${colors.dark300}`,
                      }}
                    >
                      {item.label}
                    </span>
                  )}
                  <span style={{color: colors.dark300, margin: '0 0.5rem'}}>{separator}</span>
                </>
              ) : (
                <span
                  style={{
                    fontWeight: 'bold',
                    color: colors.black,
                    paddingBottom: '0.125rem',
                    borderBottom: `0.0625rem solid ${colors.black}`,
                  }}
                >
                  {item.label}
                </span>
              )}
            </React.Fragment>
          );
        })}
      </nav>
    </div>
  );
};

export default Breadcrumb;
