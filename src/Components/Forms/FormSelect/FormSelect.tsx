'use client';

import type {InputProps} from 'Components/Input/Input';
import SelectInput from 'Components/Input/select-input';
import {type FormikValues, useFormikContext} from 'formik';
import {getValue} from 'Helpers/utils';
import type React from 'react';
import {useCallback} from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface FormSelectProps extends Omit<InputProps, 'onChange'> {
  id: string;
  options: SelectOption[];
  placeholder?: string;
  disabled?: boolean;
  onChange?: (val: string) => void;
}

const FormSelect: React.FC<FormSelectProps> = ({id, disabled, options, placeholder, ...rest}) => {
  const {setFieldValue, errors, touched, values} = useFormikContext<FormikValues>();
  const isError = !!getValue(errors, id) && getValue(touched, id) && !disabled;

  const handleChangeEvent = useCallback((val: string) => setFieldValue(id, val), [id, setFieldValue]);

  return (
    <SelectInput
      id={id}
      value={getValue(values, id)}
      errorMessage={isError ? getValue(errors, id) : ''}
      disabled={disabled}
      onChange={handleChangeEvent}
      options={options}
      placeholder={placeholder}
      {...rest}
    />
  );
};

export default FormSelect;
