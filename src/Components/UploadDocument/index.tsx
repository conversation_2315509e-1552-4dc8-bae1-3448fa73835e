import {Box, Grid, List, ListItem, Typography} from '@mui/material';
import FileDropZone from 'Components/FileDropZone';
import FileViewCard from 'Components/FileViewCard';
import {PermissionsEnum} from 'Constants/enums';
import {fileExtensionsToUpload, handleDownload} from 'Helpers/utils';
import {useSnackbar} from 'notistack';
import React, {Dispatch, SetStateAction, useCallback, useEffect, useState} from 'react';
import {DropzoneOptions} from 'react-dropzone/.';
import {IFile} from 'types';

interface IProps {
  primaryText?: string;
  onUpload: (files: File[]) => void;
  onRemoveFile: (files: File[]) => void;
  files?: File[];
  dropzoneProps?: DropzoneOptions;
  enableReinitialize?: boolean;
  existingFiles?: IFile[];
  onRemoveExistingFile?: Dispatch<SetStateAction<IFile[]>>;
  notRenderCancelBtn?: boolean;
  renderFileUpload?: boolean;
  cancelPermissions?: PermissionsEnum[];
  fileUploadPermissions?: PermissionsEnum[];
}

const UploadDocuments: React.FC<IProps> = props => {
  const {
    primaryText = '1. Please upload a signed copy to further the process.',
    onRemoveFile,
    onUpload,
    files,
    dropzoneProps,
    enableReinitialize,
    existingFiles,
    onRemoveExistingFile,
    cancelPermissions,
    fileUploadPermissions,
  } = props;

  const {enqueueSnackbar} = useSnackbar();
  const [acceptedFiles, setAcceptedFiles] = useState<File[]>(files || []);

  useEffect(() => {
    if (enableReinitialize && files?.[0]?.name) {
      setAcceptedFiles(files);
    } else if (!files?.length && enableReinitialize) {
      setAcceptedFiles([]);
    } else {
      // do nothing
    }
  }, [enableReinitialize, files]);

  const handleFileDrop = useCallback(
    (filesArg: File[]) => {
      const maxFiles = dropzoneProps?.maxFiles;
      if (maxFiles !== undefined) {
        const totalFiles = acceptedFiles.length + filesArg.length;
        if (totalFiles > maxFiles) {
          enqueueSnackbar(`You can only upload up to ${maxFiles} files.`, {variant: 'error'});
          return;
        }
      }
      setAcceptedFiles(prev => [...prev, ...filesArg]);
      onUpload(filesArg);
    },
    [dropzoneProps?.maxFiles, onUpload, acceptedFiles, enqueueSnackbar],
  );

  const handleFileRemove = (fileName: string, index: number) => {
    const newFiles = [...acceptedFiles];
    newFiles.splice(index, 1);
    setAcceptedFiles(newFiles);
    onRemoveFile(newFiles);
  };
  const handleExistingFileRemove = (index: number) => {
    if (existingFiles && onRemoveExistingFile) {
      const newFiles = [...existingFiles];
      newFiles.splice(index, 1);
      onRemoveExistingFile(newFiles);
    }
  };

  return (
    <Grid
      container
      sx={{height: '37.5rem', boxSizing: 'border-box', padding: '0.625rem'}}
      spacing={2}
      alignItems="stretch"
    >
      <Grid size={{xs: 12, sm: 5}} sx={{height: '100%', display: 'flex', flexDirection: 'column'}}>
        <Typography
          sx={{
            display: 'flex',
            justifyContent: 'flex-start',
            color: 'secondary.linkBreadcrumb',
            fontSize: 15,
            paddingBottom: 1,
            fontWeight: 'bold',
          }}
        >
          Upload documents
        </Typography>
        <FileDropZone
          onDrop={handleFileDrop}
          accept={fileExtensionsToUpload}
          dropzoneProps={dropzoneProps}
          files={acceptedFiles ?? []}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 7}} sx={{height: '100%', display: 'flex', flexDirection: 'column'}}>
        <Typography
          sx={{
            display: 'flex',
            justifyContent: 'flex-start',
            color: 'secondary.linkBreadcrumb',
            fontSize: 15,
            paddingBottom: 1,
            fontWeight: 'bold',
          }}
        >
          Attached Files ({acceptedFiles.length})
        </Typography>
        <List sx={{color: 'secondary.linkBreadcrumb', flex: 1, border: 1, borderRadius: 1, borderColor: '#C7C7C7'}}>
          {!acceptedFiles.length && (
            <ListItem
              sx={{
                position: 'relative',
                display: 'flex',
                justifyContent: 'center', // horizontal center
                alignItems: 'center', // set a fixed height (adjust as needed)
                top: '45%',
              }}
            >
              <Typography
                sx={{
                  textAlign: 'center',
                  fontWeight: '600',
                  fontSize: 22,
                  color: '#C7C7C7',
                }}
              >
                {primaryText}
              </Typography>
            </ListItem>
          )}

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              maxHeight: '12rem',
              overflowY: 'auto',
              scrollbarWidth: 'thin',
            }}
          >
            {acceptedFiles.map((file, index) => (
              <ListItem key={file.name}>
                <FileViewCard fileDetail={file} handleRemoveFile={() => handleFileRemove(file.name, index)} />
              </ListItem>
            ))}
            {existingFiles?.map((file, index) => (
              <ListItem key={file.id ?? file.originalName ?? index}>
                <FileViewCard
                  fileDetail={file}
                  cancelPermissions={cancelPermissions}
                  handleRemoveFile={() => {
                    handleExistingFileRemove(index);
                  }}
                  handleDownload={() => {
                    handleDownload(file.url, file.originalName, '_blank');
                  }}
                />
              </ListItem>
            ))}
          </Box>
        </List>
      </Grid>
    </Grid>
  );
};

export default UploadDocuments;
