import * as yup from 'yup';
export const steps = ['Tenant Details', 'Plan Details', 'Documents'];

export const countryCodes = [{code: '+1', label: 'USA'}];

export type PlanSelectedType = {
  planId: string | undefined;
  name: string | undefined;
  duration: string | undefined;
  amount: string | number | undefined;
  billingCycleId: string | undefined;
};
export interface CountryCode {
  code: string;
  label: string;
}

// US States array for the state dropdown
export const usStates = [
  {value: 'AL', label: 'Alabama'},
  {value: 'AK', label: 'Alaska'},
  {value: 'AZ', label: 'Arizona'},
  {value: 'AR', label: 'Arkansas'},
  {value: 'CA', label: 'California'},
  {value: 'CO', label: 'Colorado'},
  {value: 'CT', label: 'Connecticut'},
  {value: 'DE', label: 'Delaware'},
  {value: 'FL', label: 'Florida'},
  {value: 'GA', label: 'Georgia'},
  {value: 'HI', label: 'Hawaii'},
  {value: 'ID', label: 'Idaho'},
  {value: 'IL', label: 'Illinois'},
  {value: 'IN', label: 'Indiana'},
  {value: 'IA', label: 'Iowa'},
  {value: 'KS', label: 'Kansas'},
  {value: 'KY', label: 'Kentucky'},
  {value: 'LA', label: 'Louisiana'},
  {value: 'ME', label: 'Maine'},
  {value: 'MD', label: 'Maryland'},
  {value: 'MA', label: 'Massachusetts'},
  {value: 'MI', label: 'Michigan'},
  {value: 'MN', label: 'Minnesota'},
  {value: 'MS', label: 'Mississippi'},
  {value: 'MO', label: 'Missouri'},
  {value: 'MT', label: 'Montana'},
  {value: 'NE', label: 'Nebraska'},
  {value: 'NV', label: 'Nevada'},
  {value: 'NH', label: 'New Hampshire'},
  {value: 'NJ', label: 'New Jersey'},
  {value: 'NM', label: 'New Mexico'},
  {value: 'NY', label: 'New York'},
  {value: 'NC', label: 'North Carolina'},
  {value: 'ND', label: 'North Dakota'},
  {value: 'OH', label: 'Ohio'},
  {value: 'OK', label: 'Oklahoma'},
  {value: 'OR', label: 'Oregon'},
  {value: 'PA', label: 'Pennsylvania'},
  {value: 'RI', label: 'Rhode Island'},
  {value: 'SC', label: 'South Carolina'},
  {value: 'SD', label: 'South Dakota'},
  {value: 'TN', label: 'Tennessee'},
  {value: 'TX', label: 'Texas'},
  {value: 'UT', label: 'Utah'},
  {value: 'VT', label: 'Vermont'},
  {value: 'VA', label: 'Virginia'},
  {value: 'WA', label: 'Washington'},
  {value: 'WV', label: 'West Virginia'},
  {value: 'WI', label: 'Wisconsin'},
  {value: 'WY', label: 'Wyoming'},
];

// Extract valid state values for validation
const validStateValues = usStates.map(state => state.value);

export const addTenantValidationSchema = yup.object({
  company: yup
    .string()
    .required('Company is required')
    .min(3, 'Company should have at least 3 characters')
    .max(50, 'Company should have at most 50 characters')
    .matches(
      /^(?![-\s])[a-zA-Z0-9\s&.,'’-]+(?<![-\s])$/,
      'Company name should only contain letters, numbers, spaces, and valid punctuation in between.',
    ),

  key: yup
    .string()
    .required('Subdomain is required')
    .min(3, 'Minimum 3 characters required')
    .max(63, 'Maximum 63 characters allowed')
    .matches(
      /^(?!-)[a-z0-9-]{3,63}(?<!-)$/,
      'Subdomain should only contain lowercase letters, numbers, and hyphen in between.',
    ),

  firstName: yup
    .string()
    .required('First Name is required')
    .min(3, 'First Name should have at least 3 characters')
    .max(50, 'First Name should have at most 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'First Name should only contain letters'),

  lastName: yup
    .string()
    .required('Last Name is required')
    .min(3, 'Last Name should have at least 3 characters')
    .max(50, 'Last Name should have at most 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Last Name should only contain letters'),

  designation: yup
    .string()
    .min(3, 'Designation should have at least 3 characters')
    .max(50, 'Designation should have at most 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Designation should only contain letters'),

  email: yup
    .string()
    .required('Email is required')
    .email('Invalid email address')
    .max(50, 'Email should have at most 50 characters')
    .matches(/^[a-zA-Z0-9._%+-]+@([a-zA-Z0-9-]+\.){1,3}[a-zA-Z]{2,7}$/, 'Invalid email address'),

  countryCode: yup
    .object()
    .shape({
      value: yup.string(),
      label: yup.string(),
    })
    .required('Country Code is required'),
  city: yup
    .string()
    .required('City is required')
    .matches(/^[A-Za-z ]+$/, 'City can only contain letters and spaces')
    .min(2, 'City must be at least 2 characters')
    .max(50, 'City cannot exceed 50 characters'),

  mobileNumber: yup.string().matches(/^\d{10}$/, 'Mobile Number must be exactly 10 digits'),
  // Added state validation
  state: yup
    .string()
    .required('State is required')
    .oneOf(validStateValues, 'Please select a valid state')
    .test('is-valid-state', 'Please select a valid state from the list', value => {
      if (!value) return false;
      return validStateValues.includes(value);
    }),
});

export interface FormAddTenant {
  firstName: string;
  lastName: string;
  company: string;
  designation: string;
  email: string;
  countryCode: CountryCode;
  billingCycle: string;
  mobileNumber: string;
  language: string;
  selectedPlan: string;
  files: File[];
  key: string;
  city: string;
  state: string;
}

export const initialAddTenantValues: FormAddTenant = {
  firstName: '',
  lastName: '',
  company: '',
  designation: '',
  email: '',
  key: '',
  countryCode: {code: '+1', label: 'USA'},
  mobileNumber: '',
  billingCycle: 'Monthly',
  language: 'English',
  selectedPlan: '',
  files: [],
  city: '',
  state: '',
};

export enum TenantCreationStepType {
  TenantDetails = 0,
  PlanDetails = 1,
  Documents = 2,
}
