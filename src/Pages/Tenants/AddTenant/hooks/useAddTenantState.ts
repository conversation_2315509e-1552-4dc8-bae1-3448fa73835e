import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {PlanSelectedType, TenantCreationStepType} from '../addTenantsUtils';

export const useAddTenantState = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState<TenantCreationStepType>(0);
  const [nextButtonState, setNextButtonState] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [cluster, setCluster] = useState('');
  const [overAllPlan, setOverAllPlan] = useState<PlanSelectedType>();

  const handleNext = useCallback(
    () => setActiveStep(current => Math.min(current + 1, 2) as TenantCreationStepType),
    [],
  );

  const handleBack = useCallback(
    () =>
      setActiveStep(current => {
        if (current === TenantCreationStepType.PlanDetails) {
          setNextButtonState(false);
        }
        if (current === TenantCreationStepType.Documents && overAllPlan) {
          setNextButtonState(false);
        }
        return Math.max(0, current - 1) as TenantCreationStepType;
      }),
    [overAllPlan],
  );

  const handleNextButton = useCallback((state: boolean) => {
    setNextButtonState(state);
  }, []);

  const onNavigateToTenant = () => {
    setActiveStep(TenantCreationStepType.TenantDetails);
    setFiles([]);
    setCluster('');
    setOverAllPlan(undefined);
    setIsDialogOpen(false);
    navigate('/tenants/create-tenant');
  };

  return {
    activeStep,
    handleNext,
    handleBack,
    handleNextButton,
    files,
    cluster,
    overAllPlan,
    isDialogOpen,
    setFiles,
    setCluster,
    setOverAllPlan,
    setIsDialogOpen,
    onNavigateToTenant,
    nextButtonState,
    setActiveStep,
  };
};
