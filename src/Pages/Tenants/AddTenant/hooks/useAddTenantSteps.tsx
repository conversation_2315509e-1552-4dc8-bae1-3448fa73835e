import UploadDocuments from 'Components/UploadDocument';
import {MAX_FILES, MAX_FILE_SIZE} from 'Helpers/utils';
import {useMemo} from 'react';
import AddTenantDetails from '../AddTenantDetail';
import {PlanSelectedType, TenantCreationStepType} from '../addTenantsUtils';
import ChoosePlan from '../PlanSection/ChoosePlan';

interface UseAddTenantStepsProps {
  handleNextButton: (state: boolean) => void;
  overAllPlan: PlanSelectedType | undefined;
  onHandlePlan: (plan: PlanSelectedType) => void;
  files: File[];
  onFileUpload: (files: File[]) => void;
  onRemoveFile: (files: File[]) => void;
}

export const useAddTenantSteps = (props: UseAddTenantStepsProps) => {
  const stepComponents = useMemo<Record<TenantCreationStepType, React.ReactNode>>(
    () => ({
      0: <AddTenantDetails />,
      1: (
        <ChoosePlan
          handleNextButton={props.handleNextButton}
          overAllPlan={props.overAllPlan}
          onHandlePlan={props.onHandlePlan}
        />
      ),
      2: (
        <UploadDocuments
          primaryText="0 Files attached"
          onUpload={props.onFileUpload}
          onRemoveFile={props.onRemoveFile}
          files={props.files}
          dropzoneProps={{maxFiles: MAX_FILES, maxSize: MAX_FILE_SIZE, multiple: true}}
        />
      ),
    }),
    [props],
  );

  const renderStepContent = (step: TenantCreationStepType) => stepComponents[step] || null;

  return {
    stepComponents,
    renderStepContent,
  };
};
