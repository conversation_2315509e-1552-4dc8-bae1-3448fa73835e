import {FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {getErrorMessage} from 'Helpers/utils';
import {FormikValues} from 'formik';
import {useState} from 'react';
import {FormAddTenant, initialAddTenantValues, TenantCreationStepType} from '../addTenantsUtils';

interface FormActions {
  resetForm: (values: {values: FormAddTenant}) => void;
  setErrors: (errors: Partial<FormikValues>) => void;
}

interface UseAddTenantFormProps {
  files: File[];
  setDialogOpen: (open: boolean) => void;
  setActiveStep: (step: TenantCreationStepType) => void;
}

interface UseAddTenantFormReturn {
  initialValues: FormAddTenant;
  formSubmit: (values: FormAddTenant, actions: FormActions) => Promise<void>;
  // isLoading: boolean;
}

export const useAddTenantForm = ({
  files,
  setDialogOpen,
  setActiveStep,
}: UseAddTenantFormProps): UseAddTenantFormReturn => {
  // const [createTenant, {isLoading}] = useCreateTenantMutation();
  const [initialValues, setInitialValues] = useState<FormAddTenant>(initialAddTenantValues);

  const formSubmit = async (values: FormAddTenant, actions: FormActions): Promise<void> => {
    const formData = createFormData(values, files);

    try {
      // await createTenant(formData).unwrap();

      actions.resetForm({values: initialValues});
      setDialogOpen(true);
    } catch (error) {
      handleFormError(error, actions, setActiveStep);
    }
  };

  return {
    initialValues,
    formSubmit,
  };
};

const createFormData = (values: FormAddTenant, files: File[]): FormData => {
  const formData = new FormData();
  formData.append('name', values.company);
  formData.append('lang', values.language);

  if (values.selectedPlan) {
    formData.append('selectedPlan', JSON.stringify(values.selectedPlan));
  }

  formData.append(
    'contact',
    JSON.stringify({
      firstName: values.firstName,
      lastName: values.lastName,
      isPrimary: true,
      email: values.email,
      phoneNumber: values.mobileNumber,
      countryCode: values.countryCode.code,
      designation: values.designation,
    }),
  );

  files.forEach(file => {
    formData.append('files', file);
  });

  formData.append('key', values.key);
  return formData;
};

const handleFormError = (
  error: unknown,
  actions: FormActions,
  setActiveStep: (step: TenantCreationStepType) => void,
): void => {
  const errorMessage = getErrorMessage(error as FetchBaseQueryError);
  if (errorMessage.includes('Subdomain')) {
    setActiveStep(0);
    actions.setErrors({key: errorMessage});
  } else if (errorMessage.includes('email')) {
    setActiveStep(0);
    actions.setErrors({email: errorMessage});
  } else if (errorMessage.includes('Duplicate feature')) {
    setActiveStep(1);
  }
};
function useCreateTenantMutation(): [any, {isLoading: any}] {
  throw new Error('Function not implemented.');
}
