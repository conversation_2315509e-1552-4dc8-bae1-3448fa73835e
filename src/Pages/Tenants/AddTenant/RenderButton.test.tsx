import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import { Mock, vi } from 'vitest';
import RenderButton from './RenderButton';

// Mock Formik Context
vi.mock('formik', () => ({
  useFormikContext: vi.fn(),
}));

import { useFormikContext } from 'formik';

describe('RenderButton Component', () => {
  const mockHandleBack = vi.fn();
  const mockHandleNext = vi.fn();
  const mockHandleCancel = vi.fn();
  const mockHandleSubmit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormikContext as Mock).mockReturnValue({
      isValid: true,
      dirty: true,
      handleSubmit: mockHandleSubmit,
      values: {
        company: 'Test Company',
        key: 'test-key',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        city: 'Test City',
        state: 'CA',
      },
    });
  });

  it('renders Next button for step 0 and triggers handleNext', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    expect(screen.getByTestId('next-button')).toHaveTextContent('Plan Details >');
    fireEvent.click(screen.getByTestId('next-button'));
    expect(mockHandleNext).toHaveBeenCalled();
  });

  it('renders Back button for step 1 and triggers handleBack', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={1}
      />,
    );

    expect(screen.getByTestId('back-button')).toHaveTextContent('< Tenant Details');
    fireEvent.click(screen.getByTestId('back-button'));
    expect(mockHandleBack).toHaveBeenCalled();
  });

  it('disables Next button when shouldDisableNext is true', () => {
    (useFormikContext as Mock).mockReturnValueOnce({
      isValid: false,
      dirty: false,
      handleSubmit: mockHandleSubmit,
      values: {
        company: '',
        key: '',
        firstName: '',
        lastName: '',
        email: '',
        city: '',
        state: '',
      },
    });

    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    expect(screen.getByTestId('next-button')).toBeDisabled();
  });

  it('disables Next button when required fields are not filled', () => {
    (useFormikContext as Mock).mockReturnValueOnce({
      isValid: true,
      dirty: true,
      handleSubmit: mockHandleSubmit,
      values: {
        company: 'Test Company',
        key: '', // Missing required field
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        city: 'Test City',
        state: 'CA',
      },
    });

    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    expect(screen.getByTestId('next-button')).toBeDisabled();
  });

  it('triggers handleCancel when Cancel button is clicked', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    fireEvent.click(screen.getByTestId('cancel-button'));
    expect(mockHandleCancel).toHaveBeenCalled();
  });
});
