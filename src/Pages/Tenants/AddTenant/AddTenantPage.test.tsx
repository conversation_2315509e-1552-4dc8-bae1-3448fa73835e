/// <reference types="vitest/globals" />
import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import {Mock, vi} from 'vitest';
import AddTenantPage from './AddTenantPage';

// Mock Child Components
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  default: ({items}: any) => <div data-testid="breadcrumb">{items?.length} breadcrumbs</div>,
}));

vi.mock('Components/Forms/Form', () => ({
  default: ({children}: any) => <form data-testid="form">{children}</form>,
}));

vi.mock('Components/StepperTab/StepperTab', () => ({
  default: ({activeStep}: any) => <div data-testid="stepper">Step {activeStep}</div>,
}));

vi.mock('./PlanSection/SuccessDialog', () => ({
  default: ({isDialogOpen}: any) => (isDialogOpen ? <div data-testid="success-dialog">Dialog Open</div> : null),
}));

vi.mock('./RenderButton', () => ({
  default: ({handleCancel}: any) => (
    <button data-testid="cancel-btn" onClick={handleCancel}>
      Cancel
    </button>
  ),
}));

// Mock Hooks
vi.mock('./hooks/useAddTenantState', () => ({
  useAddTenantState: vi.fn(),
}));
vi.mock('./hooks/useAddTenantForm', () => ({
  useAddTenantForm: vi.fn(),
}));
vi.mock('./hooks/useAddTenantSteps', () => ({
  useAddTenantSteps: vi.fn(),
}));

// Import mocked hooks after mocking them
import {useAddTenantForm} from './hooks/useAddTenantForm';
import {useAddTenantState} from './hooks/useAddTenantState';
import {useAddTenantSteps} from './hooks/useAddTenantSteps';

// Mock navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('AddTenantPage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default Mock Implementation
    (useAddTenantState as Mock).mockReturnValue({
      activeStep: 0,
      handleNext: vi.fn(),
      handleBack: vi.fn(),
      handleNextButton: vi.fn(),
      files: [],
      cluster: null,
      overAllPlan: null,
      isDialogOpen: false,
      setFiles: vi.fn(),
      setCluster: vi.fn(),
      setOverAllPlan: vi.fn(),
      onNavigateToTenant: vi.fn(),
      setIsDialogOpen: vi.fn(),
      setActiveStep: vi.fn(),
      nextButtonState: true,
    });

    (useAddTenantForm as unknown as Mock).mockReturnValue({
      initialValues: {name: ''},
      formSubmit: vi.fn(),
    });

    (useAddTenantSteps as Mock).mockReturnValue({
      renderStepContent: vi.fn().mockReturnValue(<div data-testid="step-content">Step Content</div>),
    });
  });

  it('renders all main sections correctly', () => {
    render(
      <MemoryRouter>
        <AddTenantPage />
      </MemoryRouter>,
    );

    expect(screen.getByTestId('AddTenantPage')).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
    expect(screen.getByTestId('stepper')).toHaveTextContent('Step 0');
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('step-content')).toHaveTextContent('Step Content');
  });

  it('opens success dialog when isDialogOpen is true', () => {
    (useAddTenantState as unknown as Mock).mockReturnValueOnce({
      ...useAddTenantState(),
      isDialogOpen: true,
    });

    render(
      <MemoryRouter>
        <AddTenantPage />
      </MemoryRouter>,
    );

    expect(screen.getByTestId('success-dialog')).toHaveTextContent('Dialog Open');
  });

  it('navigates back to tenants when cancel button is clicked', () => {
    render(
      <MemoryRouter>
        <AddTenantPage />
      </MemoryRouter>,
    );

    fireEvent.click(screen.getByTestId('cancel-btn'));
    expect(mockNavigate).toHaveBeenCalledWith('/tenants');
  });

  it('renders with uploaded files', () => {
    (useAddTenantState as unknown as Mock).mockReturnValueOnce({
      ...useAddTenantState(),
      files: [new File(['content'], 'test.pdf', {type: 'application/pdf'})],
    });

    render(
      <MemoryRouter>
        <AddTenantPage />
      </MemoryRouter>,
    );

    expect(screen.getByTestId('form')).toBeInTheDocument();
  });
});
