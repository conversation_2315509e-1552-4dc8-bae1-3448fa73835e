import React from 'react';
import {PlanSelectedType} from '../addTenantsUtils';

interface ChoosePlanProps {
  handleNextButton?: (state: boolean) => void;
  overAllPlan?: any; // Replace `any` with the actual type later
  onHandlePlan?: (plan: PlanSelectedType) => void;
}

const ChoosePlan: React.FC<ChoosePlanProps> = ({handleNextButton, onHandlePlan}) => {
  const handleChoose = () => {
    onHandlePlan && onHandlePlan({name: 'Dummy Plan'} as PlanSelectedType);
    handleNextButton && handleNextButton(true);
  };

  return (
    <div
      style={{
        border: '0.125rem solid #4CAF50',
        padding: '1.25rem',
        borderRadius: '0.5rem',
        textAlign: 'center',
        color: '#333',
        height: '100%',
        boxSizing: 'border-box',
      }}
    >
      <h3>Choose Plan (Placeholder)</h3>
      <p>This is a dummy component. Actual plan selection will be added later.</p>
      <button
        style={{
          backgroundColor: '#4CAF50',
          color: '#fff',
          padding: '0.5rem 1rem',
          border: 'none',
          borderRadius: '0.25rem',
          cursor: 'pointer',
        }}
        onClick={handleChoose}
      >
        Select Dummy Plan
      </button>
    </div>
  );
};

export default ChoosePlan;
