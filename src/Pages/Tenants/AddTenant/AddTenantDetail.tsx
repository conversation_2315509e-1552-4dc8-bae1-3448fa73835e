import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import {
  Box,
  CircularProgress,
  Grid,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import { REGISTERED_DOMAIN } from 'Constants/enums';
import { useFormikContext } from 'formik';
import { StyleUtils } from 'Helpers/styleUtils';
import { debounce } from 'lodash';
import { colors } from 'Providers/theme/colors';
import { FC, useCallback, useEffect, useState } from 'react';
import { useVerifyTenantKeyMutation } from 'redux/app/tenantManagementApiSlice';
import { AnyObject } from 'yup';
import Icon from '../../../Assets/Icons.svg';
import { countryCodes, FormAddTenant, usStates } from './addTenantsUtils';

interface Props {
  isEdit?: boolean;
}

const AddTenantDetail: FC<Props> = ({ isEdit }) => {
  const { values, setFieldValue } = useFormikContext<FormAddTenant>();

  const [verifyTenantKey] = useVerifyTenantKeyMutation();
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);


  const debouncedVerify = useCallback(
    debounce(async (key: string) => {
      if (!key.trim()) return;
      setLoading(true);
      try {
        const res: any = await verifyTenantKey({ key }).unwrap();
        setIsAvailable(res.available);
        setSuggestions(res.available ? [] : res.suggestions || []);
      } catch (error) {
        console.error('Verify Key Error:', error);
        setIsAvailable(false);
      } finally {
        setLoading(false);
      }
    }, 500),
    [],
  );

  useEffect(() => {
    if (values.key && !isEdit) {
      debouncedVerify(values.key);
    }
  }, [values.key, debouncedVerify, isEdit]);

  let sxPropsValue: AnyObject =
    isAvailable === true
      ? {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: colors.green,
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: colors.green,
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: colors.green,
        },
      }
      : {
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: colors.errorColor,
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: colors.errorColor,
        },
      };


  if (isAvailable === null) sxPropsValue = {};

  if (isAvailable === true && values.key.length < 3) {
    sxPropsValue = {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: colors.dark400,
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: colors.dark400,
      },
    };
  }


  return (
    <Grid container spacing={2} rowSpacing={3.5} sx={{ height: '100%', boxSizing: 'border-box', padding: 2 }}>
      <Grid size={{ xs: 12 }}>
        <Typography variant="h6" fontWeight={600} sx={{ marginBottom: '-0.9rem' }}>
          Tenant Information
        </Typography>
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>Company *</Typography>
        <FormInput
          fullWidth
          id="company"
          name="company"
          required={true}
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
          placeholder="Enter company name"
        />
      </Grid>

      <Grid container spacing={0} size={{ xs: 12, sm: 6 }} alignItems="stretch">
        <Grid size={{ xs: 9 }}>
          <Typography sx={StyleUtils.lalelStyles}>Subdomain *</Typography>
          <Box sx={{ position: 'relative', display: 'inline-block', width: '100%' }}>
            <FormInput
              fullWidth
              id="key"
              name="key"
              required={true}
              sx={StyleUtils.inputBoxStyles}
              sxProps={sxPropsValue}
              placeholder="Enter subdomain"
              readOnly={isEdit}
              endAdornment={
                values.key.length >= 3 ? (
                  <InputAdornment position="end" sx={{ pr: 1 }}>
                    {loading ? (
                      <CircularProgress size={20} />
                    ) : isAvailable === true ? (
                      <img
                        src={Icon}
                        alt="Available"
                        style={{
                          width: 20,
                          height: 20,
                          objectFit: 'contain',
                        }}
                      />
                    ) : isAvailable === false ? (
                      <CancelOutlinedIcon
                        color="error"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => {
                          setFieldValue('key', ''); // Clear input
                          setIsAvailable(null); // Reset availability (hides suggestions)
                          setSuggestions([]); // Clear suggestions
                        }}
                      />
                    ) : null}
                  </InputAdornment>
                ) : null
              }
            />

            {/* ✅ Suggestions Dropdown */}
            {isAvailable === false && (
              <Box
                sx={{
                  border: `0.0625rem solid ${colors.lightGrey200}`,
                  mt: 1,
                  borderRadius: 1,
                  background: colors.white,
                  position: 'absolute',
                  zIndex: 1,
                  width: '100%', // Matches FormInput width
                }}
              >
                <Typography sx={{ fontSize: '0.85rem', p: 1 }}>
                  <Typography component="span" sx={{ color: colors.errorColor100, fontSize: '0.85rem' }}>
                    <strong>"{values.key}" is already taken.</strong>
                  </Typography>
                  <br />
                  here are some suggestions.
                </Typography>

                {suggestions.length > 0 && (
                  <Grid
                    container
                    justifyContent="center" // Centers all items
                    alignItems="center"
                  >
                    {suggestions.map(suggestion => (
                      <Grid
                        size={{ xs: 12 }}
                        key={suggestion}
                        sx={{ display: 'flex', justifyContent: 'center' }} // Centers each item
                      >
                        <List
                          dense
                          sx={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            p: 0, // removes default padding-top & bottom
                            m: 0,
                          }}
                        >
                          <ListItem
                            disablePadding
                            sx={{
                              backgroundColor: colors.white200,
                              borderRadius: 2,
                              width: '96%', // Controls width but still centered
                              mb: 1,
                            }}
                          >
                            <ListItemButton
                              data-testid={`suggestion-${suggestion}`}
                              onClick={() => setFieldValue('key', suggestion)}
                              sx={{ fontSize: '0.9rem' }}
                            >
                              {suggestion + REGISTERED_DOMAIN}
                            </ListItemButton>
                          </ListItem>
                        </List>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            )}
          </Box>
        </Grid>

        <Grid size={{ xs: 3 }}>
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              paddingTop: '1.2rem',
              alignItems: 'center',
              justifyContent: 'flex-start', // aligns text left
              pl: 0.5,
            }}
          >
            <Typography>{REGISTERED_DOMAIN}</Typography>
          </Box>
        </Grid>
      </Grid>

      <Grid size={{ xs: 12 }}>
        <Typography variant="h6" fontWeight={600} sx={{ marginBottom: '-0.9rem' }}>
          Contact Information
        </Typography>
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>First name *</Typography>
        <FormInput
          fullWidth
          id="firstName"
          name="firstName"
          placeholder="Enter first name"
          required={true}
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>Last name *</Typography>
        <FormInput
          fullWidth
          id="lastName"
          name="lastName"
          placeholder="Enter last name"
          required={true}
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>Job title</Typography>
        <FormInput
          fullWidth
          id="designation"
          name="designation"
          placeholder="Enter job title"
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>Primary phone number</Typography>
        <FormInput
          fullWidth
          id="mobileNumber"
          name="mobileNumber"
          sx={inputBoxStyles}
          placeholder="Enter primary phone number"
          onInput={e => {
            const target = e.target as HTMLInputElement;
            target.value = target.value.replace(/\D/g, '');
          }}
          readOnly={isEdit}
          sxProps={{
            paddingLeft: 0,
          }}
          startAdornment={
            <InputAdornment
              position="start"
              sx={{
                ...StyleUtils.inputAdornment,
              }}
            >
              <Select
                data-testid="country-code-select"
                variant="standard"
                disableUnderline
                disabled={true}
                defaultValue={values?.countryCode?.code}
                sx={{
                  ...StyleUtils.selectBox,
                }}
                IconComponent={() => null}
              >
                {countryCodes.map(option => (
                  <MenuItem key={option.code} value={option.code} data-testid={`country-code-${option.code}`}>
                    {option.code}
                  </MenuItem>
                ))}
              </Select>
            </InputAdornment>
          }
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>Email address *</Typography>
        <FormInput
          fullWidth
          id="email"
          name="email"
          type="email"
          required={true}
          placeholder="Enter email address"
          sx={StyleUtils.inputBoxStyles}
          readOnly={isEdit}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>City *</Typography>
        <FormInput
          fullWidth
          id="city"
          name="city"
          placeholder="Enter city"
          required={true}
          sx={StyleUtils.inputBoxStyles}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }}>
        <Typography sx={StyleUtils.lalelStyles}>State *</Typography>
        <FormSelect
          fullWidth
          id="state"
          name="state"
          required={true}
          sx={StyleUtils.selectBoxStyles}
          placeholder="Select state"
          readOnly={isEdit}
          options={usStates}
        />
      </Grid>
    </Grid>
  );
};

export default AddTenantDetail;
