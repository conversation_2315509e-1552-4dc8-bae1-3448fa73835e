// import React from "react";

import {Box, Grid, Paper, Stack} from '@mui/material';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import breadcrumbItems from 'Components/Breadcrumb/routes';
import Form from 'Components/Forms/Form';
import StepperTab from 'Components/StepperTab/StepperTab';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {addTenantValidationSchema, steps} from './addTenantsUtils';
import {useAddTenantForm} from './hooks/useAddTenantForm';
import {useAddTenantState} from './hooks/useAddTenantState';
import {useAddTenantSteps} from './hooks/useAddTenantSteps';
import SuccessDialog from './PlanSection/SuccessDialog';
import RenderButton from './RenderButton';

const AddTenantPage = () => {
  const navigate = useNavigate();

  const {
    activeStep,
    handleNext,
    handleBack,
    handleNextButton,
    files,
    cluster,
    overAllPlan,
    isDialogOpen,
    setFiles,
    setCluster,
    setOverAllPlan,
    onNavigateToTenant,
    setIsDialogOpen,
    setActiveStep,
    nextButtonState,
  } = useAddTenantState();

  const {initialValues, formSubmit} = useAddTenantForm({
    files,
    setDialogOpen: setIsDialogOpen,
    setActiveStep,
  });

  const {renderStepContent} = useAddTenantSteps({
    handleNextButton,
    overAllPlan,
    onHandlePlan: setOverAllPlan,
    files,
    onFileUpload: (uploadedFiles: File[]) => setFiles([...files, ...uploadedFiles]),
    onRemoveFile: setFiles,
  });

  const handleCancel = useCallback(() => {
    navigate('/tenants');
  }, [navigate]);

  return (
    <Box sx={{paddingInline: 2, borderRadius: 2}} data-testid="AddTenantPage">
      <Stack spacing={2}>
        <Breadcrumb items={breadcrumbItems} separator="|" showHeader />
      </Stack>

      <Paper
        elevation={6}
        sx={{
          mb: 2,
          borderRadius: 1,
          minHeight: 600,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Grid container>
          <Grid size={{xs: 12}}>
            <StepperTab activeStep={activeStep} steps={steps} />
          </Grid>
          <Grid size={{xs: 12}}>
            <Form
              initialValues={initialValues}
              enableReinitialize
              validationSchema={addTenantValidationSchema} // Add here
              onSubmit={formSubmit}
            >
              <Box sx={{flex: 1, height: '100%'}}>{renderStepContent(activeStep)}</Box>

              <SuccessDialog isDialogOpen={isDialogOpen} onNavigateToTenant={onNavigateToTenant} />
              <RenderButton
                handleBack={handleBack}
                handleNext={handleNext}
                handleCancel={handleCancel}
                activeStep={activeStep}
                nextButtonState={nextButtonState}
                isFileUploaded={files.length > 0}
              />
            </Form>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AddTenantPage;
