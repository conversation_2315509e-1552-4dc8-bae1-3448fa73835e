import '@testing-library/jest-dom';
import {render, screen, waitFor} from '@testing-library/react';
import {Mock, vi} from 'vitest';
import AddTenantDetail from './AddTenantDetail';

// Mock Formik Context
vi.mock('formik', () => ({
  useFormikContext: vi.fn(),
}));

// Mock Custom Form Components
vi.mock('Components/Forms/FormInput', () => ({
  __esModule: true,
  default: ({id, placeholder, endAdornment, startAdornment, onBlur}: any) => (
    <div data-testid={`form-input-${id}`}>
      <input data-testid={`input-${id}`} placeholder={placeholder} onBlur={onBlur} />
      {endAdornment && <div data-testid={`endAdornment-${id}`}>{endAdornment}</div>}
      {startAdornment && <div data-testid={`startAdornment-${id}`}>{startAdornment}</div>}
    </div>
  ),
}));

vi.mock('Components/Forms/FormSelect/FormSelect', () => ({
  __esModule: true,
  default: ({id, placeholder}: any) => (
    <select data-testid={`form-select-${id}`}>
      <option>{placeholder}</option>
    </select>
  ),
}));

// Mock redux API call
const mockVerifyTenantKey = vi.fn();
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useVerifyTenantKeyMutation: () => [mockVerifyTenantKey],
}));

// Mock StyleUtils (to prevent style errors)
vi.mock('Helpers/styleUtils', () => ({
  StyleUtils: {
    labelStyle: {},
    inputStyles: {},
    selectBox: {},
    labelStyleAdornment: {},
    getBaseInputStyle: {},
    inputAdornment: {},
  },
}));

// Mock Icon
vi.mock('../../../Assets/Icons.png', () => ({
  default: 'mocked-icon.png',
}));

// Import after mocks
import {useFormikContext} from 'formik';

describe('AddTenantDetail Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useFormikContext as Mock).mockReturnValue({
      values: {
        key: '',
        countryCode: {code: '+1'},
      },
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {},
    });
  });

  it('renders all major sections', () => {
    render(<AddTenantDetail />);
    expect(screen.getByText('Tenant Information')).toBeInTheDocument();
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-company')).toBeInTheDocument();
    expect(screen.getByTestId('form-select-state')).toBeInTheDocument();
  });

  it('calls verifyTenantKey when key changes', async () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'validKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    mockVerifyTenantKey.mockResolvedValueOnce({
      unwrap: () => Promise.resolve({available: true}),
    });

    render(<AddTenantDetail />);
    await waitFor(() => {
      expect(mockVerifyTenantKey).toHaveBeenCalledWith({key: 'validKey'});
    });
  });

  it('shows available icon when key is available', async () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'validKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    mockVerifyTenantKey.mockResolvedValueOnce({
      unwrap: () => Promise.resolve({available: true}),
    });

    render(<AddTenantDetail />);
    await waitFor(() => {
      expect(screen.getByTestId('endAdornment-key')).toBeInTheDocument();
    });
  });

  it('renders startAdornment with country codes', () => {
    render(<AddTenantDetail />);
    expect(screen.getByTestId('startAdornment-mobileNumber')).toBeInTheDocument();
  });
});
