---
name: snyk

on:
  push:
    branches:
      - '**' # matches every branch
      - '!main' # excludes main
  pull_request:
    branches:
      - main

jobs:
  security:
    runs-on: ubuntu-latest
    name: snyk
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: Vulnerability scan
        uses: snyk/actions/iac@master
        with:
          command: monitor
          args: --severity-threshold=low

      - name: Set up Node 16
        uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: install Snyk CLI
        run: npm install -g snyk
      - uses: actions/checkout@master
      - name: snyk monitor
        run: snyk test --report
        env:
          SNYK_TOKEN: ${{ secrets.ARC_SNYK_TOKEN }}
